<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVariant value="50:Theia-based"/>
	<ccsVersion value="51.5.0"/>
	<deviceFamily value="TMS470"/>
	<connection value="common/targetdb/connections/uart_connection.xml"/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=empty_mspm0g3507_nortos_ticlang.projectspec.empty_mspm0g3507_nortos_ticlang,buildProfile=release,isHybrid=true"/>
	<activeTargetConfiguration value="targetConfigs/MSPM0G3507.ccxml"/>
	<isTargetConfigurationManual value="false"/>
	<sourceLookupPath value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"/>
	<origin value="D:/BaiduNetdiskDownload/8.程序源码汇总/8.程序源码汇总/MSPM0底盘传感器扩展源码/四驱/四路巡线模块/CCS/FourWayPortal_USART"/>
	<filesToOpen value="empty_mspm0g3507.syscfg,README.md"/>
</projectOptions>
